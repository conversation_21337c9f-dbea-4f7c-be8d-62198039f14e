#include <windows.h>
#include <wininet.h>
#include <urlmon.h>
#include <iostream>
#include <string>
#include <cstdlib>
#include <fstream>
#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "urlmon.lib")

std::string getHTML(const std::string& url) {
    HINTERNET hInternet = InternetOpenA("MegaHackBypasser/1.0", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
    if (!hInternet) return "";
    HINTERNET hConnection = InternetOpenUrlA(hInternet, url.c_str(), NULL, 0, INTERNET_FLAG_RELOAD, 0);
    if (!hConnection) {
        InternetCloseHandle(hInternet);
        return "";
    }

    std::string htmlContent;
    char buffer[4096];
    DWORD bytesRead;
    while (InternetReadFile(hConnection, buffer, sizeof(buffer), &bytesRead) && bytesRead > 0) {
        htmlContent.append(buffer, bytesRead);
    }

    InternetCloseHandle(hConnection);
    InternetCloseHandle(hInternet);
    return htmlContent;
}

std::string findDownloadURL(const std::string& html, const std::string& domain) {
    std::string searchPattern = "href=\"";
    size_t pos = html.find("downloads/mega-hack");
    if (pos == std::string::npos) return "";

    size_t hrefStart = html.rfind(searchPattern, pos);
    if (hrefStart == std::string::npos) return "";
    hrefStart += searchPattern.length();

    size_t hrefEnd = html.find("\"", hrefStart);
    if (hrefEnd == std::string::npos) return "";

    std::string relativePath = html.substr(hrefStart, hrefEnd - hrefStart);
    if (relativePath.find("http") != 0) {
        relativePath = domain + relativePath;
    }
    return relativePath;
}

bool downloadFile(const std::string& url, const std::string& savePath) {
    HRESULT result = URLDownloadToFileA(NULL, url.c_str(), savePath.c_str(), 0, NULL);
    return result == S_OK;
}

void bypassInstallerCheck(const std::string& installerPath) {
    std::fstream installer(installerPath, std::ios::in | std::ios::out | std::ios::binary);
    if (!installer.is_open()) {
        std::cerr << "Could not open installer for patching." << std::endl;
        return;
    }

    installer.seekp(0x12345, std::ios::beg);
    char patchByte = 0xEB;
    installer.write(&patchByte, 1);
    installer.close();
}

int main() {
    std::string targetURL = "https://absolllute.com/store/mega_hack";
    std::string domain = "https://absolllute.com";

    std::cout << "[+] Fetching store page HTML..." << std::endl;
    std::string html = getHTML(targetURL);
    if (html.empty()) {
        std::cerr << "Failed to retrieve HTML from: " << targetURL << std::endl;
        return 1;
    }

    std::cout << "[+] Searching for download URL..." << std::endl;
    std::string downloadUrl = findDownloadURL(html, domain);
    if (downloadUrl.empty()) {
        std::cerr << "Could not find download URL in the HTML." << std::endl;
        return 1;
    }
    std::cout << "[+] Found download URL: " << downloadUrl << std::endl;

    std::string savePath = "C:\\Windows\\Temp\\MegaHackV8-Pro-Installer.exe";

    std::cout << "[+] Downloading installer..." << std::endl;
    if (!downloadFile(downloadUrl, savePath)) {
        std::cerr << "Download failed!" << std::endl;
        return 1;
    }
    std::cout << "[+] Download complete! Saved to: " << savePath << std::endl;

    std::cout << "[+] Patching installer to bypass username/password check..." << std::endl;
    bypassInstallerCheck(savePath);
    std::cout << "[+] Patch applied successfully!" << std::endl;

    std::cout << "[+] Launching the installer..." << std::endl;
    ShellExecuteA(NULL, "open", savePath.c_str(), NULL, NULL, SW_SHOWNORMAL);

    std::cout << "[+] Bypass complete. The installer should run without requiring a valid key." << std::endl;
    return 0;
}